package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
	"slices"
)

var _ = Factory.reg(basic.NewGeneral[*m100002])

type m100002 struct {
	Config c100002
}

func (m m100002) ID() int32 {
	return 100002
}

func (m m100002) Line() int32 {
	return 25
}

func (m m100002) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m100002) Exception(code int32) string {
	return games.S100002{}.Exception(code)
}

func (g *m100002) Init(config []byte) {
	g.Config = utils.ParseYAML[c100002](config)
}

func (m m100002) ZeroSpin(ctl int32, salt *rand.Rand) basic.ISpin {
	for {
		spin := m.randomSpin(salt)
		pay := spin.Payout()
		if pay == 0 {
			return spin
		}
	}
}

// 所有的图标不包含万能图标和免费
func (m *m100002) iconsNotWildNotFree() []basic.Ico {
	icons := m.iconsNotFree()
	s := []basic.Ico{}
	for i := 0; i < len(icons); i++ {
		if !m.isCommon(icons[i]) {
			s = append(s, icons[i])
		}
	}
	return s
}

// 所有的图标不包含免费图标
func (m *m100002) iconsNotFree() []basic.Ico {
	icons := m.icons()
	index := slices.Index(icons, m.Config.FreeSpin.Icon)
	icons = append(icons[:index], icons[index+1:]...)
	return icons
}

// 所有的图标
func (m *m100002) icons() []basic.Ico {
	return utils.MapKeysOrdered(m.Config.PayoutTable)
}

// 随机生成一个网格
func (m *m100002) randomNormalGrid(rd *rand.Rand) []basic.Ico {
	icons := m.icons()
	iconsNotWildFree := m.iconsNotWildNotFree()
	grid := utils.RandomFrom(icons, m.Config.Column*m.Config.Row, rd)
	freeCount := 0
	for i := int32(0); i < m.Config.Row*m.Config.Column; i++ {
		icon := grid[i]
		if i%int32(m.Config.Column) == 0 && m.isCommon(icon) { //第一列图标不能是万能图标
			grid[i] = utils.RandomPick(iconsNotWildFree, 1, rd)[0]
			icon = grid[i]
		}
		if m.isFreeSpin(icon) { //正常模式不能有免费图标
			if freeCount >= 3 {
				grid[i] = utils.RandomPick(iconsNotWildFree, 1, rd)[0]
				icon = grid[i]
			} else {
				freeCount++
			}
		}
	}
	return grid
}

// 随机生成一个网格
func (m *m100002) randomGrid(rd *rand.Rand) []basic.Ico {
	// icons := m.iconsNotWildNotFree()
	// iconsNotFree := m.iconsNotFree()
	// grid := make([]basic.Ico, m.Config.Row*m.Config.Column)
	// for i := int32(0); i < m.Config.Row*m.Config.Column; i++ {
	// 	if i%int32(m.Config.Column) == 0 { //  第一列图标不能是万能图标
	// 		grid[i] = utils.RandomPick(icons, 1, rd)[0]
	// 	} else {
	// 		grid[i] = utils.RandomPick(iconsNotFree, 1, rd)[0]
	// 	}
	// }
	icons := m.icons()
	iconsNotWildFree := m.iconsNotWildNotFree()
	grid := utils.RandomFrom(icons, m.Config.Column*m.Config.Row, rd)
	for i := int32(0); i < m.Config.Row*m.Config.Column; i++ {
		icon := grid[i]
		if i%int32(m.Config.Column) == 0 && m.isCommon(icon) { //第一列图标不能是万能图标
			grid[i] = utils.RandomPick(iconsNotWildFree, 1, rd)[0]
			icon = grid[i]
		}
		if m.isFreeSpin(icon) { //正常模式不能有免费图标
			grid[i] = utils.RandomPick(iconsNotWildFree, 1, rd)[0]
			icon = grid[i]
		}
	}
	return grid
}

// 随机生成一个网格免费过程中  可以带免费图标
func (m *m100002) randomGridFreeing(rd *rand.Rand) []basic.Ico {
	icons := m.icons()
	iconsNotWildFree := m.iconsNotWildNotFree()
	grid := utils.RandomFrom(icons, m.Config.Column*m.Config.Row, rd)
	for i := int32(0); i < m.Config.Row*m.Config.Column; i++ {
		icon := grid[i]
		if i%int32(m.Config.Column) == 0 && m.isCommon(icon) {
			grid[i] = utils.RandomPick(iconsNotWildFree, 1, rd)[0]
			icon = grid[i]
		}
	}
	return grid
}
func (m *m100002) randomFreeSpin(rd *rand.Rand) games.S100002 {
	freeCount := rd.Intn(6) + 4 // 4-9
	var payout uint64 = 0
	var pages []games.P100002
	grid := m.randomGrid(rd)
	freePositions := utils.RandomBetweenN(0, len(grid)-1, freeCount, rd)
	for _, p := range freePositions {
		grid[p] = m.Config.FreeSpin.Icon
	}
	page := m.parseToPage(grid)
	payout += uint64(page.Pay)
	pages = append(pages, page)
	next := true
	var freeSpins int16 = 0
	for next {
		nextGrid, newfreePositions := m.randomFreePagesNext(freePositions, rd)
		page := m.parseToPage(nextGrid)
		pages = append(pages, page)
		if len(newfreePositions) > len(freePositions) {
			next = true
		} else {
			next = false
		}
		freePositions = newfreePositions
		freeCount := page.FreeCount
		if freeCount > 9 {
			freeCount = 9
		}
		freeSpins = m.Config.FreeSpin.Count[freeCount-1]
		payout += uint64(page.Pay)
	}
	for j := 0; j < int(freeSpins); j++ {
		grid = m.randomGridFreeing(rd)
		page := m.parseToPage(grid)
		page.FreeDoing = true
		freeCount := page.FreeCount
		if freeCount > 0 {
			if freeCount > 9 {
				freeCount = 9
			}
			page.Pay += m.Config.SpecialPayoutTable[3][freeCount-1]
		}
		payout += uint64(page.Pay)
		pages = append(pages, page)
	}
	return games.S100002{
		Pages: pages,
		Pays:  int32(payout),
	}
}

func (m *m100002) randomFreePagesNext(freePositions []int, rd *rand.Rand) ([]basic.Ico, []int) {
	grid := m.randomGrid(rd)
	for _, p := range freePositions {
		grid[p] = m.Config.FreeSpin.Icon
	}
	next := false
	if utils.RandomBetween(1, 3, rd) > 2 {
		next = true
	}
	if next {
		ps := utils.RandomBetweenN(0, len(grid)-1, 1, rd)
		p := ps[0]
		if slices.Index(freePositions, p) >= 0 {
			next = false
		} else {
			grid[p] = m.Config.FreeSpin.Icon
			freePositions = append(freePositions, p)
		}
	}
	return grid, freePositions
}

func (m m100002) Spin(rd *rand.Rand) basic.ISpin {
	if rd.Intn(100) < 2 {
		return m.randomFreeSpin(rd)
	} else {
		return m.randomSpin(rd)
	}
}

// 随机生成非免费的spin
func (m *m100002) randomSpin(rd *rand.Rand) games.S100002 {
	grid := m.randomNormalGrid(rd)
	page := m.parseToPage(grid)
	if page.Pay > m.Config.MaxPayout {
		return games.S100002{Pays: -1}
	}
	return games.S100002{
		Pages: []games.P100002{page},
		Pays:  page.Pay,
	}
}

func (m *m100002) parseToPage(grid []basic.Ico) games.P100002 {
	var payout int32 = 0
	lines := []games.L100002{}
	for i, pattern := range m.Config.Pattern {
		index := pattern[0].Index(m.Config.Column)
		firstIco := grid[index]
		count := 0
		wild2count := 0
		iconsNo := []int32{}
		special1 := false
		special2 := false
		for _, pos := range pattern {
			index := pos.Index(m.Config.Column)
			ico := grid[index]
			if (!m.isConnection(firstIco, ico) && !m.isCommon(ico)) || m.isFreeSpin(firstIco) {
				break
			}
			if firstIco == 1 || firstIco == 2 {
				if ico != firstIco { //宝箱组合
					if !m.isCommon(ico) {
						special1 = true
					}
				}
			}
			if firstIco == 3 || firstIco == 4 || firstIco == 5 {
				if ico != firstIco { //红包组合
					if !m.isCommon(ico) {
						special2 = true
					}
				}
			}
			count++
			if ico == 10 {
				wild2count++
			}
			// iconsNo = append(iconsNo, pos.Row()+pos.Column()*3)
			//旋转之后的坐标
			iconsNo = append(iconsNo, index/m.Config.Column+index%m.Config.Column*m.Config.Row)
		}
		if count > 0 {
			if add := m.Config.PayoutTable[firstIco][count-1]; add > 0 {
				if special1 {
					add = m.Config.SpecialPayoutTable[1][count-1]
				} else if special2 {
					add = m.Config.SpecialPayoutTable[2][count-1]
				}
				x := (int32(wild2count) * 2)
				if x > 0 {
					add *= x
				}
				payout += add
				lines = append(lines, games.L100002{
					SerialNo: int(i),
					IconsNo:  iconsNo,
					Payout:   int(add),
				})
			}
		}
	}
	freeCount := 0
	for _, icon := range grid {
		if m.isFreeSpin(icon) {
			freeCount++
		}
	}
	return games.P100002{
		Grid:      grid,
		Pay:       payout,
		FreeCount: int16(freeCount),
		Lines:     lines,
	}
}

// 俩个图标是否可连线
func (m *m100002) isConnection(ico1 basic.Ico, ico2 basic.Ico) bool {
	return ico1 == ico2 || ((ico1 == 1 || ico1 == 2) && (ico2 == 1 || ico2 == 2)) || ((ico1 == 3 || ico1 == 4 || ico1 == 5) && (ico2 == 3 || ico2 == 4 || ico2 == 5))
}

func (m m100002) Salting(spin0 basic.ISpin, salt *rand.Rand) basic.ISpin {
	spin := spin0.(games.S100002)
	spin.Column = m.Config.Column
	spin.Row = m.Config.Row
	spin.FreeSpinIcon = m.Config.FreeSpin.Icon
	return spin
}

func (m m100002) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m *m100002) isCommon(ico basic.Ico) bool {
	// return m.Config.CommonIcon == ico
	return slices.Contains(m.Config.CommonIcons, ico)
}

func (m *m100002) isFreeSpin(ico basic.Ico) bool {
	return m.Config.FreeSpin.Icon == ico
}

func (m m100002) InputCoef(ctl int32) int32 {
	return 100
}

// 不同的算法模式的最小赔率 单线
func (m m100002) MinPayout(ctl int32) int32 {
	return 0
}

type c100002 struct {
	Row                int32
	Column             int32
	Pattern            [][]basic.Position
	PayoutTable        map[basic.Ico][]int32
	SpecialPayoutTable map[int32][]int32
	CommonIcons        []basic.Ico
	FreeSpin           struct {
		Icon          basic.Ico
		Count         []int16
		FreeModeIcons map[basic.Ico]int32
	}
	MaxPayout int32
}
